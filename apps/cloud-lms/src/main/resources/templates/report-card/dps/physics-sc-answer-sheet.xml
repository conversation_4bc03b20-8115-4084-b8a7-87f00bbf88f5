<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xi="http://www.w3.org/2001/XInclude"
         xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body  margin="12mm"  />
        </fo:simple-page-master>
    </fo:layout-master-set>
    <!--first-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(17, 18)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(19, 20)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(21, 22)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(23, 24)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(25, 26)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--fourth-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(27, 28)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--fifth-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(29, 30)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--six-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(31, 31)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="200mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--seventh-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(32, 32)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="200mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--eight-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(33, 33)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="200mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>