package com.wexl.retail.util;

import static com.wexl.retail.util.Constants.BACK_SLASH;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.model.Student;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class UploadService {

  private final AuthService authService;
  private final StrapiService strapiService;
  private final ExamRepository examRepository;
  private final StorageService storageService;
  private final StudentService studentService;
  private final ContentService contentService;

  public S3FileUploadResult generatePresignedUrlToUploadAnswerSheet(long examId) {

    var filePath = constructAnswerSheetFilePath(examId, "%s.pdf".formatted(examId));
    return S3FileUploadResult.builder()
        .path(filePath)
        .url(storageService.generatePreSignedUrlForUpload(filePath))
        .build();
  }

  public String constructAnswerSheetFilePath(Exam exam, String fileName) {
    // This method can be executed by a teacher or a student
    String organization = authService.getUserDetails().getOrganization();
    Student student = studentService.getStudentById(exam.getStudentId());
    String gradeSlug = contentService.getGradeById(student.getClassId()).getSlug();
    return organization
        .concat(BACK_SLASH + gradeSlug)
        .concat(BACK_SLASH + exam.getSubjectId())
        .concat(BACK_SLASH + exam.getId())
        .concat(BACK_SLASH + fileName);
  }

  public String constructAnswerSheetFilePath(Long examId, String fileName) {
    var exam = examRepository.getById(examId);
    return constructAnswerSheetFilePath(exam, fileName);
  }

  @Deprecated(forRemoval = true)
  public InputStream getS3ObjectInputStream(Long examId) {
    String organization = authService.getUserDetails().getOrganization();
    var exam = examRepository.getById(examId);
    Student student = studentService.getStudentById(exam.getStudentId());
    String gradeSlug = contentService.getGradeById(student.getClassId()).getSlug();
    String filePath =
        organization
            .concat(BACK_SLASH + gradeSlug)
            .concat(BACK_SLASH + exam.getSubjectId())
            .concat(BACK_SLASH + exam.getId())
            .concat(BACK_SLASH + exam.getId() + ".pdf");
    return storageService.getInputStream(filePath);
  }

  public S3FileUploadResult uploadPdfAnswerSheets(Long examId) {
    String filePath = constructAnswerSheetFilePath(examId, "%s.pdf".formatted(examId));
    return S3FileUploadResult.builder()
        .path(filePath)
        .url(storageService.generatePreSignedUrlForUpload(filePath))
        .previewUrl(storageService.generatePreSignedUrlForFetch(filePath))
        .build();
  }

  /**
   * @deprecated By {@link #generatePresignedUrlToUploadAnswerSheet(long)}
   *     <p>will upload file directly from client machine to S3 bucket instead of routing it through
   *     retail-service by providing S3PresignedUrl of <code>PUT</code> operation.
   * @see StorageService#generatePreSignedUrlForUpload(String)
   * @param examId The Id of current exam.
   * @param multipartFiles List of files to be uploaded to S3 bucket.
   * @return List of {@link S3FileUploadResult}
   * @throws IOException If any errors occurred while uploading files to S3 bucket.
   */
  @Deprecated(forRemoval = true)
  public List<S3FileUploadResult> uploadAnswerSheets(
      Long examId, List<MultipartFile> multipartFiles) {

    List<S3FileUploadResult> uploadResults = new ArrayList<>();

    for (MultipartFile multipartFile : multipartFiles) {
      String fileName = multipartFile.getOriginalFilename();
      String filePath = constructAnswerSheetFilePath(examId, fileName);
      storageService.uploadFile(multipartFile, filePath);
      uploadResults.add(
          S3FileUploadResult.builder()
              .path(filePath)
              .url(storageService.generatePreSignedUrlForFetch(filePath))
              .build());
    }

    return uploadResults;
  }
}
