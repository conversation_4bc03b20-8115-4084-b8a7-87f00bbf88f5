package com.wexl.retail.student.exam;

import com.wexl.retail.student.answer.ExamAnswer;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

@Repository
public interface ExamAnswerRepository extends JpaRepository<ExamAnswer, Long> {

  Optional<ExamAnswer> findByExamAndQuestionUuidAndType(
      Exam exam, String questionUuid, String type);

  List<ExamAnswer> findByExam(Exam savedExam);

  @Modifying
  @Transactional
  void deleteByExam(@NotNull Exam exam);
}
