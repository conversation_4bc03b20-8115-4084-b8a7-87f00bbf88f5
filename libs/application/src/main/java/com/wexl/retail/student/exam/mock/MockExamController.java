package com.wexl.retail.student.exam.mock;

import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.elp.dto.ElpDto;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.dto.QuestionDto;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class MockExamController {

  private final MockExamService mockExamService;

  @GetMapping("/students/{studentId}/mock-exams/{examId}/questions")
  public QuestionDto.QuestionResponse getAllSchoolTestQuestions(@Valid @PathVariable long examId) {
    return mockExamService.getQuestions(examId);
  }

  @IsStudent
  @PostMapping("/mock-exams:submit")
  public void submitElpTest(
      @RequestBody ElpDto.StudentAnswerRequest studentAnswerRequest, @PathVariable String orgSlug) {
    mockExamService.submitMockExam(studentAnswerRequest, orgSlug);
  }

  @IsStudent
  @GetMapping("/mock-exams/{examId}/results")
  public QuestionDto.StudentResultsResponse getExamResult(@PathVariable long examId) {
    return mockExamService.getExamResult(examId);
  }

  @IsStudent
  @GetMapping("/mock-exams/test-definition/{testDefinitionId}/instructions")
  public QuestionDto.QuestionsResponse getInstructions(@PathVariable Long testDefinitionId) {
    return mockExamService.getInstructions(testDefinitionId);
  }

  @IsStudent
  @GetMapping("/students/{authUserId}/speaking-exams")
  public List<QuestionDto.LabExamResponse> getBetWritingExams(
      @PathVariable String authUserId, @PathVariable String orgSlug) {
    return mockExamService.getStudentBetLabExamsByCategory(
        authUserId, orgSlug, TestCategory.SPEAK_LAB);
  }

  @IsStudent
  @GetMapping("/students/{authUserId}/writing-exams")
  public List<QuestionDto.LabExamResponse> getBetSpeakingExams(
      @PathVariable String authUserId, @PathVariable String orgSlug) {
    return mockExamService.getStudentBetLabExamsByCategory(
        authUserId, orgSlug, TestCategory.WRITE_LAB);
  }

  @IsStudent
  @GetMapping("/students/{authUserId}/placement-exams")
  public List<QuestionDto.LabExamResponse> getBetPlacementExams(
      @PathVariable String authUserId, @PathVariable String orgSlug) {
    return mockExamService.getStudentBetLabExamsByCategory(
        authUserId, orgSlug, TestCategory.PLACEMENT_LAB);
  }

  @IsStudent
  @PostMapping("/students/{authUserId}/lab-exams")
  public ExamResponse startBetExam(@RequestBody @Valid QuestionDto.LabExamRequest labExamRequest) {
    return mockExamService.startLabExam(labExamRequest);
  }

  @IsStudent
  @PostMapping("/students/{authUserId}/lab-exams/{examId}")
  public QuestionDto.SubmitResponse submitBetExam(
      @PathVariable String orgSlug, @PathVariable Long examId) {
    return mockExamService.submitLabExam(examId, orgSlug);
  }
}
