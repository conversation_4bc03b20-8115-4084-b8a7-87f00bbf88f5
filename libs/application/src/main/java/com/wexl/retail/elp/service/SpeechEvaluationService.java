package com.wexl.retail.elp.service;

import static com.wexl.retail.content.model.QuestionType.SPCH;
import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.speech.SpeechService;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SpeechEvaluationService {
  private final ExamRepository examRepository;
  private final ContentService contentService;
  private final List<SpeechService> speechServices;
  private static final String IMPROMPTU_SPEECH = "Impromptu Speech";

  @Value("${app.contentToken}")
  String contentBearerToken;

  public SpeechEvaluation.SpeechResponse evaluateSpeakingTest(long examId, String questionUuid) {
    final Optional<Exam> possibleExam = examRepository.findById(examId);
    if (possibleExam.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }

    final Exam exam = possibleExam.get();
    final ExamAnswer answer =
        exam.getExamAnswers().stream()
            .filter(examAnswer -> examAnswer.getQuestionUuid().equals(questionUuid))
            .findFirst()
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput"));
    if (answer.getSpchSelectedAnswer() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.speechQuestionUnanswered");
    }
    return evaluateSpeakingTestByExamAnswer(answer);
  }

  public SpeechEvaluation.SpeechResponse evaluateSpeakingTestByExamAnswer(ExamAnswer answer) {
    final String speechAnswer = answer.getSpchSelectedAnswer();
    if (Objects.isNull(speechAnswer)) {
      return null;
    }
    var speechResponse =
        speechServices.getFirst().pronunciationAssessment(String.valueOf(answer.getId()));
    if (Objects.nonNull(speechResponse)) {
      return speechResponse;
    }
    var testQuestion =
        contentService.getQuestionsByUuid(
            contentBearerToken,
            SPCH.getType().toUpperCase(),
            answer.getQuestionUuid(),
            WEXL_INTERNAL);
    if (testQuestion.questions().isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "error.Invalid.Elp.Question.Org",
          new String[] {answer.getQuestionUuid()});
    }

    return speechServices
        .getFirst()
        .pronunciationAssessment(
            massage(testQuestion.questions().getFirst().question()),
            speechAnswer,
            String.valueOf(answer.getId()),
            testQuestion.questions().getFirst().category().equals(IMPROMPTU_SPEECH));
  }

  public String massage(String question) {
    // this is a temporary hack till we fix the questions.
    String s = question.replace("<p>", "").replace("</p>", "");
    s = s.toLowerCase().replace("say the sentence", "");
    return s;
  }

  public void deleteSpeechResponse(String reference) {
    speechServices.getFirst().pronunciationAssessment(reference, true);
  }

  public Double migratePhonetics(ExamAnswer examAnswer) {
    var speechResponse =
        speechServices.getFirst().pronunciationAssessment(String.valueOf(examAnswer.getId()));

    if (speechResponse != null) {
      String remarks = speechResponse.assessment().remarks();

      if (remarks != null
          && remarks.contains(
              "error_unknown_words : The server could not identify the following words: strong>")) {
        deleteSpeechResponse(String.valueOf(examAnswer.getId()));
      }
    }

    var response = evaluateSpeakingTestByExamAnswer(examAnswer);
    return response == null
        ? 0.0
        : (response.assessment().accuracyScore() / 100) * examAnswer.getMarksPerQuestion();
  }

  @Async
  public void speakingEvaluationAsync(
      String question, String answer, String examAnswerId, boolean isImpromptu) {
    try {
      speechServices
          .getFirst()
          .pronunciationAssessment(question, answer, examAnswerId, isImpromptu);
    } catch (Exception e) {
      log.info("Error while evaluating speaking test for answer: {}", examAnswerId, e);
    }
  }
}
