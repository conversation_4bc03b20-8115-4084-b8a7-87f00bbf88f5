package com.wexl.erp.medical.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.model.GuardianRole;
import java.util.List;
import lombok.Builder;

public record MedicalProfileDto() {
  @Builder
  public record Request(
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("date_of_birth") Long dateOfBirth,
      @JsonProperty("height_cm") String height,
      @JsonProperty("weight_kg") String weight,
      @JsonProperty("drug_or_medicines") String drugOrMedicines,
      @JsonProperty("food_allergy") String foodAllergy,
      @JsonProperty("guardians") List<GuardianData> guardianDetails,
      @JsonProperty("allergies") Allergies allergies,
      @JsonProperty("illness") Illness illness,
      @JsonProperty("chronic_diseases") String chronicDiseases,
      @JsonProperty("heart_condition") String heartCondition,
      @JsonProperty("surgery_or_admitted_hospital") String surgeryOrAdmittedHospital,
      @JsonProperty("wears_spectacles") String wearsSpectacles,
      @JsonProperty("dental_treatment") String dentalTreatment,
      @JsonProperty("remarks") Remarks remarks) {}

  @Builder
  public record Response(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("height_cm") String height,
      @JsonProperty("weight_kg") String weight,
      @JsonProperty("date_of_birth") Long dateOfBirth,
      @JsonProperty("guardians") List<GuardianData> guardianDetails,
      @JsonProperty("drug_or_medicines") String drugOrMedicines,
      @JsonProperty("food_allergy") String foodAllergy,
      @JsonProperty("allergies") Allergies allergies,
      @JsonProperty("illness") Illness illness,
      @JsonProperty("chronic_diseases") String chronicDiseases,
      @JsonProperty("heart_condition") String heartCondition,
      @JsonProperty("surgery_or_admitted_hospital") String surgeryOrAdmittedHospital,
      @JsonProperty("wears_spectacles") String wearsSpectacles,
      @JsonProperty("dental_treatment") String dentalTreatment,
      @JsonProperty("remarks") Remarks remarks) {}

  @Builder
  public record GuardianDetails(List<GuardianData> guardianData) {}

  @Builder
  public record GuardianData(
      @JsonProperty("guardian_name") String guardianName,
      @JsonProperty("phone_number") Long phoneNumber,
      @JsonProperty("relation_type") GuardianRole relationType) {}
}
