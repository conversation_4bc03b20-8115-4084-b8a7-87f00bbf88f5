package com.wexl.erp.fees.service.fineType;

import com.wexl.erp.fees.model.CumulativeFine;
import com.wexl.erp.fees.model.FeeGroupFeeType;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FineType;
import com.wexl.erp.fees.repository.CumulativeFineRepository;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CumulativeRule implements FineTypeRule {

  private final CumulativeFineRepository cumulativeFineRepository;

  @Override
  public boolean supports(FeeGroupFeeType feeGroupFeeType) {
    return FineType.CUMULATIVE.equals(feeGroupFeeType.getFineType());
  }

  @Override
  public Double calculateFine(FeeHead feeHead, FeeGroupFeeType feeGroupFeeType) {
    LocalDateTime dueDateTime = feeHead.getDueDate();
    if (dueDateTime == null || !dueDateTime.toLocalDate().isBefore(LocalDate.now())) {
      return 0.0;
    }

    List<CumulativeFine> cumulativeData =
        cumulativeFineRepository.findAllByFeeGroupFeeType(feeGroupFeeType);
    double totalFine = 0.0;
    LocalDate dueDate = dueDateTime.toLocalDate();
    double fineAmount =
        feeGroupFeeType.getFineAmount() == 0.0 ? 0.0 : feeGroupFeeType.getFineAmount();
    LocalDate today = LocalDate.now();
    LocalDate calcTillDate = today.minusDays(1);

    cumulativeData.sort(Comparator.comparing(CumulativeFine::getDueDate));

    if (!cumulativeData.isEmpty()) {
      LocalDate firstCumulativeDate = cumulativeData.get(0).getDueDate().toLocalDate();
      long daysBeforeFirstSlab = firstCumulativeDate.toEpochDay() - dueDate.toEpochDay();
      if (daysBeforeFirstSlab > 0) {
        totalFine += daysBeforeFirstSlab * fineAmount;
      }
    } else {
      long days = calcTillDate.toEpochDay() - dueDate.toEpochDay();
      if (days > 0) {
        totalFine += days * fineAmount;
      }
      return totalFine;
    }

    for (int i = 0; i < cumulativeData.size(); i++) {
      CumulativeFine current = cumulativeData.get(i);
      LocalDate currentDueDate =
          current.getDueDate() != null ? current.getDueDate().toLocalDate() : null;

      if (currentDueDate == null || currentDueDate.isAfter(calcTillDate)) {
        continue;
      }

      LocalDate fineStart = currentDueDate.plusDays(1);
      LocalDate fineEnd;

      if (i + 1 < cumulativeData.size()) {
        LocalDate nextDueDate = cumulativeData.get(i + 1).getDueDate().toLocalDate();
        fineEnd = nextDueDate.minusDays(1);
      } else {
        fineEnd = calcTillDate;
      }

      if (fineEnd.isAfter(calcTillDate)) {
        fineEnd = calcTillDate;
      }

      if (!fineStart.isAfter(fineEnd)) {
        long days = fineEnd.toEpochDay() - fineStart.toEpochDay() + 1;
        totalFine += days * current.getFineAmount();
      }
    }

    return totalFine;
  }
}
