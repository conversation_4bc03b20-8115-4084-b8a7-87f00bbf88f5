package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.CumulativeFine;
import com.wexl.erp.fees.model.FeeGroupFeeType;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CumulativeFineRepository extends JpaRepository<CumulativeFine, Long> {
  List<CumulativeFine> findAllByFeeGroupFeeType(FeeGroupFeeType feeGroupFeeType);
}
