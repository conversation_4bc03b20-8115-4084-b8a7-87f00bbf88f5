package com.wexl.erp.receipt.service;

import com.ibm.icu.text.RuleBasedNumberFormat;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FeePaymentDetail;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.erp.fees.repository.FeePaymentDetailsRepository;
import com.wexl.erp.receipt.dto.ReceiptDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.util.ValidationUtils;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

@Service
@Slf4j
@RequiredArgsConstructor
public class FeeReceiptService {

  private final UserRepository userRepository;
  private final ValidationUtils validationUtils;
  private final ReportCardService reportCardService;
  private final FeeHeadRepository feeHeadRepository;
  private final OfflineTestReportService offlineTestReportService;
  private final TemplateEngine templateEngine;
  private final FeePaymentDetailsRepository feePaymentDetailsRepository;
  private final ReportCardTemplateRepository reportCardTemplateRepository;

  public byte[] generateReceipt(String orgSlug, String studentAuthId, String feeHeadId) {
    User user = getUserByAuthId(studentAuthId);
    var header = buildHeader(user, orgSlug);
    var body = buildBody(user.getStudentInfo(), feeHeadId, orgSlug);

    Map<String, Object> model = Map.of("header", header, "pages", List.of(1), "body", body);
    Context context = new Context(Locale.getDefault(), Map.of("model", model));

    String template = getTemplate(orgSlug);

    String foTemplate = templateEngine.process("report-card/fee/" + template, context);
    return offlineTestReportService.generatePdf(foTemplate);
  }

  private User getUserByAuthId(String authUserId) {
    return userRepository
        .findByAuthUserId(authUserId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.SERVER_ERROR, "User not found with auth ID: " + authUserId));
  }

  public ReceiptDto.Header buildHeader(User user, String orgSlug) {
    Student student = user.getStudentInfo();
    var father = getGuardianByRelation(student, GuardianRole.FATHER);
    String fatherName = father != null ? reportCardService.getGuardianName(father) : null;
    var org = validationUtils.isOrgValid(orgSlug);

    return ReceiptDto.Header.builder()
        .studentName(user.getFirstName() + " " + user.getLastName())
        .orgSlug(orgSlug)
        .className(student.getSection().getName())
        .rollNo(student.getRollNumber())
        .schoolName(org.getName())
        .fatherName(fatherName)
        .build();
  }

  private Guardian getGuardianByRelation(Student student, GuardianRole role) {
    return student.getGuardians().stream()
        .filter(g -> role.equals(g.getRelationType()))
        .findFirst()
        .orElse(null);
  }

  public ReceiptDto.Body buildBody(Student student, String feeHeadId, String orgSlug) {
    FeeHead feeHead = getFeeHeadById(feeHeadId, orgSlug, student);
    FeePaymentDetail paymentDetail = getPaymentDetailByFeeHead(feeHead);

    String receiptDate = formatReceiptDate(paymentDetail);
    var marks = buildMarks(feeHead, paymentDetail);
    var firstTable = buildFirstTable(paymentDetail, marks);

    return ReceiptDto.Body.builder()
        .receiptDate(receiptDate)
        .paymentType(paymentDetail.getFeePayment().getPaymentMethod().name())
        .firstTable(firstTable)
        .build();
  }

  private FeeHead getFeeHeadById(String feeHeadId, String orgSlug, Student student) {
    return feeHeadRepository
        .findByIdAndOrgSlugAndStudent(UUID.fromString(feeHeadId), orgSlug, student)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.SERVER_ERROR, "Fee head not found with ID: " + feeHeadId));
  }

  private FeePaymentDetail getPaymentDetailByFeeHead(FeeHead feeHead) {

    List<FeePaymentDetail> feePaymentDetails = feePaymentDetailsRepository.findByFeeHead(feeHead);
    if (feePaymentDetails.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "No fee payment details found for fee head: " + feeHead.getId());
    }
    return feePaymentDetails.getFirst();
  }

  private String formatReceiptDate(FeePaymentDetail paymentDetail) {
    return paymentDetail
        .getCreatedAt()
        .toLocalDateTime()
        .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
  }

  private ReceiptDto.Marks buildMarks(FeeHead feeHead, FeePaymentDetail feePaymentDetail) {
    return ReceiptDto.Marks.builder()
        .sno("1")
        .description(feeHead.getFeeType().getCode())
        .amount(String.valueOf(feePaymentDetail.getAmountPaid()))
        .build();
  }

  private ReceiptDto.FirstTable buildFirstTable(
      FeePaymentDetail paymentDetail, ReceiptDto.Marks marks) {
    return ReceiptDto.FirstTable.builder()
        .totalMoney(String.valueOf(paymentDetail.getAmountPaid()))
        .totalMoneyText(convertToWords(paymentDetail.getAmountPaid()))
        .marks(Collections.singletonList(marks))
        .build();
  }

  public static String convertToWords(double amount) {
    int rupees = (int) amount;
    RuleBasedNumberFormat formatter =
        new RuleBasedNumberFormat(Locale.ENGLISH, RuleBasedNumberFormat.SPELLOUT);
    return capitalize(formatter.format(rupees)) + " Rupees Only";
  }

  private static String capitalize(String input) {
    return input == null || input.isEmpty()
        ? input
        : Character.toUpperCase(input.charAt(0)) + input.substring(1);
  }

  private String getTemplate(String orgSlug) {
    var feeTemplates =
        reportCardTemplateRepository.findByReportCardTemplateTypeAndOrgSlug(
            ReportCardTemplateType.FEE_RECEIPT, orgSlug);
    if (feeTemplates.isEmpty()) {
      return "default-fee-receipt.xml";
    }
    return feeTemplates.getFirst().getConfig();
  }
}
