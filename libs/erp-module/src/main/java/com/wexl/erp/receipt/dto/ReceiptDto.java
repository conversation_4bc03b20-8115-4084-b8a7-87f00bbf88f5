package com.wexl.erp.receipt.dto;

import java.util.List;
import lombok.Builder;

public class ReceiptDto {
  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String orgSlug,
      String studentName,
      String className,
      String fatherName,
      String rollNo) {}

  @Builder
  public record Body(String receiptDate, String paymentType, FirstTable firstTable) {}

  @Builder
  public record FirstTable(String totalMoney, String totalMoneyText, List<Marks> marks) {}

  @Builder
  public record Marks(String sno, String description, String amount) {}
}
