package com.wexl.erp.paymentGateway.model;

import com.wexl.erp.paymentGateway.dto.PaymentConfig;
import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "payment_gateway_details")
public class PaymentGatewayDetail extends Model {
  @Id @GeneratedValue private UUID id;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @Enumerated(EnumType.ORDINAL)
  @Column(name = "payment_method", nullable = false)
  private PaymentMethod paymentMethod;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private PaymentConfig config;
}
