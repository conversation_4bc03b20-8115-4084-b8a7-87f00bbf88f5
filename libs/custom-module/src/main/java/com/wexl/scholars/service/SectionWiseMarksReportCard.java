package com.wexl.scholars.service;

import com.wexl.dps.reportcard.BaseReportCardDefinition;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.OfflineTestScheduleDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestSchedule;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudent;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleRepository;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.scholars.dto.ScholarsReportCardDto;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class SectionWiseMarksReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final OfflineTestScheduleRepository offlineTestScheduleRepository;
  private final StudentRepository studentRepository;
  private final UserService userService;
  private final StudentService studentService;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    return reportCardTemplate.getConfig().equals("section-wise-report.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var testSchedule =
        offlineTestScheduleRepository
            .findById(request.offlineTestDefinitionId())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Offline Test Schedule not found"));

    var header = buildHeader(user, org, testSchedule);
    var body = buildBody(testSchedule, request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  public ScholarsReportCardDto.Header buildHeader(
      User user, Organization org, OfflineTestSchedule offlineTestSchedule) {

    var section = user.getStudentInfo().getSection();
    return ScholarsReportCardDto.Header.builder()
        .schoolName(org.getName())
        .testName(offlineTestSchedule.getOfflineTestDefinition().getTitle())
        .academicYear(offlineTestSchedule.getOfflineTestDefinition().getAcademicYearSlug())
        .className(section.getGradeName())
        .section(section.getName())
        .build();
  }

  private ScholarsReportCardDto.Body buildBody(
      OfflineTestSchedule offlineTestSchedule, ReportCardDto.Request request) {
    var firstTable = buildFirstTable(offlineTestSchedule, request);
    return ScholarsReportCardDto.Body.builder()
        .subject(offlineTestSchedule.getSubjectsMetaData().getName())
        .totalMarks(offlineTestSchedule.getMarks())
        .firstTable(firstTable)
        .build();
  }

  private ScholarsReportCardDto.FirstTable buildFirstTable(
      OfflineTestSchedule offlineTestSchedule, ReportCardDto.Request request) {
    Optional<List<OfflineTestScheduleDto.SectionWiseMarks>> sectionWiseMarksOpt =
        offlineTestSchedule.getOfflineTestScheduleStudents().stream()
            .filter(x -> Objects.nonNull(x.getSectionWiseMarks()))
            .findFirst()
            .map(OfflineTestScheduleStudent::getSectionWiseMarks)
            .stream()
            .findFirst();
    if (sectionWiseMarksOpt.isEmpty()) {
      return ScholarsReportCardDto.FirstTable.builder()
          .column1("")
          .column2("")
          .column3("")
          .column4("")
          .column5("")
          .column6("")
          .build();
    }
    var sectionWiseMarks = sectionWiseMarksOpt.get();
    var builder = ScholarsReportCardDto.FirstTable.builder();
    builder.column1(
        sectionWiseMarks.get(0).section() + " (" + sectionWiseMarks.get(0).totalMarks() + ")");
    builder.column2(
        sectionWiseMarks.size() > 1
            ? sectionWiseMarks.get(1).section() + " (" + sectionWiseMarks.get(1).totalMarks() + ")"
            : null);
    builder.column3(
        sectionWiseMarks.size() > 2
            ? sectionWiseMarks.get(2).section() + " (" + sectionWiseMarks.get(2).totalMarks() + ")"
            : null);
    builder.column4(
        sectionWiseMarks.size() > 3
            ? sectionWiseMarks.get(3).section() + " (" + sectionWiseMarks.get(3).totalMarks() + ")"
            : null);
    builder.column5(
        sectionWiseMarks.size() > 4
            ? sectionWiseMarks.get(4).section() + " (" + sectionWiseMarks.get(4).totalMarks() + ")"
            : null);
    builder.column6(
        sectionWiseMarks.size() > 5
            ? sectionWiseMarks.get(5).section() + " (" + sectionWiseMarks.get(5).totalMarks() + ")"
            : null);
    builder.total("Total" + " (" + offlineTestSchedule.getMarks().toString() + ")");
    builder.marks(buildMarks(offlineTestSchedule, request));
    return builder.build();
  }

  private List<ScholarsReportCardDto.StudentMarks> buildMarks(
      OfflineTestSchedule offlineTestSchedule, ReportCardDto.Request request) {
    List<OfflineTestScheduleStudent> offlineTestScheduleStudents;
    if (request.isAllStudents()) {
      offlineTestScheduleStudents =
          new ArrayList<>(offlineTestSchedule.getOfflineTestScheduleStudents());
    } else {
      Student student = studentService.getStudentByAuthId(request.studentAuthId());
      offlineTestScheduleStudents =
          offlineTestSchedule.getOfflineTestScheduleStudents().stream()
              .filter(x -> x.getStudentId().equals(student.getId()))
              .toList();
      if (offlineTestScheduleStudents.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "No marks found for the student in this test");
      }
    }
    var students =
        studentRepository.findStudentListById(
            offlineTestScheduleStudents.stream()
                .map(OfflineTestScheduleStudent::getStudentId)
                .toList());
    Map<Long, Student> studentMap =
        students.stream().collect(Collectors.toMap(Student::getId, Function.identity()));
    List<ScholarsReportCardDto.StudentMarks> studentMarks = new ArrayList<>();
    for (OfflineTestScheduleStudent otss : offlineTestScheduleStudents) {
      if (otss.getSectionWiseMarks() == null || otss.getSectionWiseMarks().isEmpty()) {
        studentMarks.add(
            ScholarsReportCardDto.StudentMarks.builder()
                .sno("1")
                .admissionNo(studentMap.get(otss.getStudentId()).getRollNumber())
                .name(
                    userService.getNameByUserInfo(
                        studentMap.get(otss.getStudentId()).getUserInfo()))
                .build());
      } else {
        var sectionWiseMarks = otss.getSectionWiseMarks();
        studentMarks.add(
            ScholarsReportCardDto.StudentMarks.builder()
                .sno(String.valueOf(offlineTestScheduleStudents.indexOf(otss) + 1))
                .admissionNo(studentMap.get(otss.getStudentId()).getRollNumber())
                .name(
                    userService.getNameByUserInfo(
                        studentMap.get(otss.getStudentId()).getUserInfo()))
                .sec1(
                    sectionWiseMarks.get(0).marks() != null
                        ? sectionWiseMarks.get(0).marks().toString()
                        : null)
                .sec2(
                    sectionWiseMarks.size() > 1
                        ? sectionWiseMarks.get(1).marks() != null
                            ? sectionWiseMarks.get(1).marks().toString()
                            : null
                        : null)
                .sec3(
                    sectionWiseMarks.size() > 2
                        ? sectionWiseMarks.get(2).marks() != null
                            ? sectionWiseMarks.get(2).marks().toString()
                            : null
                        : null)
                .sec4(
                    sectionWiseMarks.size() > 3
                        ? sectionWiseMarks.get(3).marks() != null
                            ? sectionWiseMarks.get(3).marks().toString()
                            : null
                        : null)
                .sec5(
                    sectionWiseMarks.size() > 4
                        ? sectionWiseMarks.get(4).marks() != null
                            ? sectionWiseMarks.get(4).marks().toString()
                            : null
                        : null)
                .sec6(
                    sectionWiseMarks.size() > 5
                        ? sectionWiseMarks.get(5).marks() != null
                            ? sectionWiseMarks.get(5).marks().toString()
                            : null
                        : null)
                .total(buildTotal(sectionWiseMarks))
                .build());
      }
    }
    return studentMarks;
  }

  private String buildTotal(List<OfflineTestScheduleDto.SectionWiseMarks> sectionWiseMarks) {
    var total =
        sectionWiseMarks.stream()
            .map(OfflineTestScheduleDto.SectionWiseMarks::marks)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    return total.toString();
  }
}
