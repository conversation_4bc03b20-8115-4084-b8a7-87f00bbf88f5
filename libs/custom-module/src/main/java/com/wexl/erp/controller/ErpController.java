package com.wexl.erp.controller;

import com.wexl.erp.dto.ErpDto;
import com.wexl.erp.service.ErpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ErpController {
  private final ErpService erpService;

  @PostMapping("/public/erp-teacher:sync")
  public ErpDto.Response syncErpTeachers(@RequestBody ErpDto.ErpEntityChangeResponse response) {
    return erpService.syncErpProcess(response);
  }

  @PostMapping("/public/student:values")
  public void sendStudentNotification(@RequestBody ErpDto.Response response) {
    erpService.sendStudentNotification(response, response.orgSlug());
  }
}
