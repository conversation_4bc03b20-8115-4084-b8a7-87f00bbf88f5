package com.wexl.erp.service;

import com.wexl.dps.model.ErpIntegration;
import com.wexl.dps.repository.ErpIntegrationRepository;
import com.wexl.erp.dto.ErpDto;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import jakarta.transaction.Transactional;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

@Service
@RequiredArgsConstructor
@Slf4j
public class ErpService {

  private final ErpTeacherProcessor erpTeacherProcessor;
  private final ErpStudentProcessor erpStudentProcessor;
  private final ErpIntegrationRepository erpIntegrationRepository;
  private final ErpFeeStudentProcessor erpFeeProcessor;
  private final TemplateEngine templateEngine;
  private final JavaMailSender emailSender;
  private final StudentRepository studentRepository;
  private final OrganizationRepository organizationRepository;

  @Value("${spring.mail.from-email}")
  private String fromEmail;

  private static final String MAIL_CONTENT_TEMPLATE = "content";

  @Transactional
  public ErpDto.Response syncErpProcess(ErpDto.ErpEntityChangeResponse response) {
    log.info("Processing response: {}", response);

    AtomicInteger addStudent = new AtomicInteger();
    AtomicInteger updateStudent = new AtomicInteger();
    AtomicInteger deleteStudent = new AtomicInteger();

    List<ErpDto.StudentProcessResponse> studentResponses = new ArrayList<>();

    response
        .erpEntityChanges()
        .forEach(
            request -> {
              if ("teacher".equals(request.type())) {
                erpTeacherProcessor.process(request);
              } else if ("student".equals(request.type())) {

                switch (request.changeType()) {
                  case "ADD" -> addStudent.getAndIncrement();
                  case "UPDATE" -> updateStudent.getAndIncrement();
                  case "DELETE" -> deleteStudent.getAndIncrement();
                  default -> log.error("student change Type not found");
                }
                var studentResponse = erpStudentProcessor.process(request);

                if (studentResponse.isEmpty()) {
                  log.warn("Student response is null for request: {}", request);
                  studentResponses.add(
                      ErpDto.StudentProcessResponse.builder()
                          .name(studentResponse.getMessage())
                          .section("N/A")
                          .grade("N/A")
                          .board("N/A")
                          .type(request.changeType())
                          .build());
                } else {
                  String name =
                      safe(studentResponse.getFirstName())
                          + " "
                          + safe(studentResponse.getLastName());
                  String section = safe(studentResponse.getSection());
                  String grade = safe(studentResponse.getGradeSlug());
                  String board = safe(studentResponse.getBoardSlug());

                  studentResponses.add(
                      ErpDto.StudentProcessResponse.builder()
                          .name(name)
                          .section(section)
                          .grade(grade)
                          .board(board)
                          .type(request.changeType())
                          .build());
                }
              } else if ("fee".equals(request.type())) {
                erpFeeProcessor.process(request, response.admissionNumbers());
              } else {
                log.info("Do not understand type.");
              }
            });

    erpIntegrationRepository.save(
        ErpIntegration.builder()
            .type(response.erpEntityChanges().getFirst().type())
            .lastSyncedAt(LocalDateTime.now())
            .json(String.valueOf(response))
            .build());

    return ErpDto.Response.builder()
        .addStudent(addStudent.get())
        .updateStudent(updateStudent.get())
        .deleteStudent(deleteStudent.get())
        .studentResponses(studentResponses)
        .build();
  }

  public void sendStudentNotification(ErpDto.Response response, String orgSlug) {

    Organization organization = organizationRepository.findBySlug(orgSlug);
    var activeStudents = studentRepository.findByOrgSlug(orgSlug);
    var inactiveStudents = studentRepository.findByOrgSlugAndDeletedAtIsNotNull(orgSlug);

    var updatedResponse =
        ErpDto.Response.builder()
            .addStudent(response.addStudent())
            .updateStudent(response.updateStudent())
            .deleteStudent(response.deleteStudent())
            .activeStudents(activeStudents.size())
            .inActiveStudents(inactiveStudents.size())
            .totalStudents(response.totalStudents())
            .studentResponses(response.studentResponses())
            .build();

    Map<String, String> recipientDetails = new HashMap<>();
    recipientDetails.put("Gowtham", "<EMAIL>");
    recipientDetails.put("Roja", "<EMAIL>");

    sendStudentDetailsEmailNotification(updatedResponse, organization, recipientDetails);
  }

  public void sendStudentDetailsEmailNotification(
      ErpDto.Response studentSummary,
      Organization organization,
      Map<String, String> recipientDetails) {
    String organizationName = organization.getName();
    JavaMailSender sender = emailSender;
    String from = fromEmail;

    for (Map.Entry<String, String> entry : recipientDetails.entrySet()) {
      String recipientName = entry.getKey();
      String recipientEmail = entry.getValue();
      try {

        // Prepare template model
        Map<String, Object> model = new HashMap<>();
        model.put("name", recipientName);
        model.put("organizationName", organizationName);

        String content =
                """

                         <p>Please find below the summary of student data synced today for the organization: <strong>%s</strong>.</p>

                         <ul>
                             <li><strong>Total Students:</strong> %d</li>
                             <li><strong>Students Added:</strong> %d</li>
                             <li><strong>Students Updated:</strong> %d</li>
                             <li><strong>Students Deleted:</strong> %d</li>
                             <li><strong>Active Students:</strong> %d</li>
                             <li><strong>Inactive Students:</strong> %d</li>
                         </ul>

                         """
                .formatted(
                    organizationName,
                    studentSummary.totalStudents(),
                    studentSummary.addStudent(),
                    studentSummary.updateStudent(),
                    studentSummary.deleteStudent(),
                    studentSummary.activeStudents(),
                    studentSummary.inActiveStudents());

        String studentTableHtml = buildStudentTableHtml(studentSummary.studentResponses());
        content += studentTableHtml;
        model.put(MAIL_CONTENT_TEMPLATE, content);

        var message = sender.createMimeMessage();
        var helper =
            new MimeMessageHelper(
                message,
                MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
                StandardCharsets.UTF_8.name());

        helper.setTo(recipientEmail);
        helper.setSubject(organizationName + " - Student Sync Summary Report 📋");
        helper.setFrom(from);

        var context = new Context();
        context.setVariables(model);
        String html = templateEngine.process("student-counts-email", context);

        helper.setText(html, true);

        log.info("Sending student sync summary email to: {}", recipientEmail);
        sender.send(message);
        log.info("Student summary email sent!");

      } catch (Exception e) {
        log.error("Error sending student summary email", e);
      }
    }
  }

  private String buildStudentTableHtml(List<ErpDto.StudentProcessResponse> studentResponses) {
    if (studentResponses == null || studentResponses.isEmpty()) {
      return "<p>No student data available for detailed report.</p>";
    }

    StringBuilder tableBuilder = new StringBuilder();

    tableBuilder.append(
        """
      <h3>Detailed Student Sync Report</h3>
      <table border='1' cellspacing='0' cellpadding='5'
             style='border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;'>
        <thead style='background-color: #f2f2f2;'>
          <tr>
            <th>S.No</th>
            <th>Name</th>
            <th>Section</th>
            <th>Grade</th>
            <th>Board</th>
            <th>Type</th>
          </tr>
        </thead>
        <tbody>
  """);

    int serialNumber = 1;
    for (ErpDto.StudentProcessResponse response : studentResponses) {
      tableBuilder.append(
              """
                <tr>
                  <td>%d</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                </tr>
              """
              .formatted(
                  serialNumber++,
                  safe(response.name()),
                  safe(response.section()),
                  safe(response.grade()),
                  safe(response.board()),
                  safe(response.type())));
    }

    tableBuilder.append("</tbody></table>");
    return tableBuilder.toString();
  }

  private String safe(String value) {
    return value == null ? "" : value;
  }
}
