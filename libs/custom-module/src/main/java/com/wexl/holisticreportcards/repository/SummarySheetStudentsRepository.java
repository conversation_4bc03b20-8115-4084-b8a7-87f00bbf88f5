package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.SummarySheetStudents;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SummarySheetStudentsRepository extends JpaRepository<SummarySheetStudents, Long> {

  Optional<SummarySheetStudents> findByStudentId(Long studentId);
}
